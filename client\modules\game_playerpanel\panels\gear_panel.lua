-- gear_panel.lua
-- Handles the gear panel content and functionality

local GearPanel = {}

function GearPanel.show(contentPanel)
  if not contentPanel then
    return
  end
  
  -- Add padding to the content panel
  contentPanel:setPaddingLeft(10)
  contentPanel:setPaddingRight(10)
  contentPanel:setPaddingTop(10)
  contentPanel:setPaddingBottom(10)
  
  -- Create a vertical layout for gear content
  local layout = UIVerticalLayout.create(contentPanel)
  layout:setSpacing(10)
  contentPanel:setLayout(layout)
  
  -- Create title
  local titleLabel = g_ui.createWidget('UILabel', contentPanel)
  titleLabel:setText('Equipment')
  titleLabel:setFont('verdana-11px-antialised')
  titleLabel:setColor('#ffffff')
  titleLabel:setTextAlign(AlignCenter)
  titleLabel:setHeight(20)
  
  -- Create gear slots container
  local gearContainer = g_ui.createWidget('UIWidget', contentPanel)
  gearContainer:setHeight(200)
  
  -- Create placeholder text for now
  local placeholderLabel = g_ui.createWidget('UILabel', gearContainer)
  placeholderLabel:setText('Gear slots will be displayed here\n(Implementation pending)')
  placeholderLabel:setFont('verdana-11px-antialised')
  placeholderLabel:setColor('#888888')
  placeholderLabel:setTextAlign(AlignCenter)
  placeholderLabel:addAnchor(AnchorHorizontalCenter, 'parent', AnchorHorizontalCenter)
  placeholderLabel:addAnchor(AnchorVerticalCenter, 'parent', AnchorVerticalCenter)
end

return GearPanel 