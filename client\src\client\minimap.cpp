/*
 * Copyright (c) 2010-2017 OTClient <https://github.com/edubart/otclient>
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 * THE SOFTWARE.
 */


#include "minimap.h"
#include "tile.h"
#include "game.h"

#include "pngunpacker.h"
#include <boost/lexical_cast.hpp>

#include <framework/graphics/image.h>
#include <framework/graphics/texture.h>
#include <framework/graphics/painter.h>
#include <framework/graphics/framebuffermanager.h>
#include <framework/core/resourcemanager.h>
#include <framework/core/filestream.h>
#include <framework/graphics/apngloader.h>
#include <zlib.h>

#include <framework/util/stats.h>
#include <framework/platform/platform.h>

MinimapCustom g_minimapCustom;

void MinimapBlock::clean()
{
    m_tiles.fill(MinimapTile());
    m_texture.reset();
    m_mustUpdate = false;
}

void MinimapBlock::update()
{
    if(!m_mustUpdate)
        return;

    ImagePtr image(new Image(Size(MMBLOCK_SIZE, MMBLOCK_SIZE)));

    bool shouldDraw = false;
    for(int x=0;x<MMBLOCK_SIZE;++x) {
        for(int y=0;y<MMBLOCK_SIZE;++y) {
            uint8 c = getTile(x, y).color;
            Color col = Color::alpha;
            if(c != 255) {
                col = Color::from8bit(c);
                shouldDraw = true;
            }
            image->setPixel(x, y, col);
        }
    }

    if(shouldDraw) {
        m_texture = TexturePtr(new Texture(image));
    } else
        m_texture.reset();

    m_mustUpdate = false;
}

void MinimapBlock::updateTile(int x, int y, const MinimapTile& tile)
{
    if(m_tiles[getTileIndex(x,y)].color != tile.color)
        m_mustUpdate = true;

    m_tiles[getTileIndex(x,y)] = tile;
}

class WordDelimitedByUnderscore : public std::string
{};

std::istream& operator>>(std::istream& is, WordDelimitedByUnderscore& output)
{
    std::getline(is, output, '_');
    return is;
}

MinimapCustom::MinimapCustom()
{}


void MinimapCustom::initFramebuffer()
{
    m_framebuffer = g_framebuffers.createFrameBuffer();
    //m_framebuffer->setBackuping(false);
    m_framebuffer->resize(
        Size(
            MINIMAP_BLOCK_PIXEL_SIZE * TEXTURE_CACHE_SIZE,
            MINIMAP_BLOCK_PIXEL_SIZE * TEXTURE_CACHE_SIZE
        )
    );
}

void MinimapCustom::init()
{
    m_loadingFuture = std::async(&MinimapCustom::loadPngs, this, MINIMAP_RENDER_ZIP);
}

void MinimapCustom::terminate()
{
    clean();
    m_framebuffer.reset();
    m_TexturesCache.clear();
}

void MinimapCustom::clean()
{
    std::lock_guard<std::mutex> lock(m_lock);
    for(int i=0;i<=Otc::MAX_Z;++i)
        m_tileBlocks[i].clear();
}

bool MinimapCustom::loadPngs(std::string dir)
{
    try {
        const auto sprDir = g_platform.getCurrentDir() + "data/minimap";

        PngUnpacker pu(sprDir);
        pu.unpackFiles();

        const std::vector<char>& packedData = pu.getPackedFileData();

        for (const auto& fileMetadata : pu.getMetadata()) {
            /* Analyze file name */
            const std::string& name = fileMetadata.getFileName();

            std::istringstream iss(name);
            std::vector<std::string> results((std::istream_iterator<WordDelimitedByUnderscore>(iss)),
                std::istream_iterator<WordDelimitedByUnderscore>());

            if (results.size() != 3) {
                continue;
            }

            /* Save data to buffer */
            try
            {
                int x = boost::lexical_cast<int>(results[0]);
                int y = boost::lexical_cast<int>(results[1]);
                int z = boost::lexical_cast<int>(results[2].substr(0, results[2].find('.')));

                m_imageExistanceCache[y][x][z] = true;

                imagesDataCache[y][x][z].resize(fileMetadata.getFileSize());
                std::copy_n(&packedData[fileMetadata.getOffset()], fileMetadata.getFileSize(), imagesDataCache[y][x][z].begin());
            }
            catch (boost::bad_lexical_cast&)
            {
                // just skip that one
            }
        }
    }
    catch (stdext::exception&) {
        m_isLoadingFinished = true;
        return false;
    }

    m_isLoadingFinished = true;
    return true;
}

void MinimapCustom::update(const Position& cameraCenterPos, bool reinitCache)
{
    Position pos = normalizePosition(cameraCenterPos);
    int currBlockNumberX = pos.x / TILES_IN_MINIMAP_BLOCK;
    int currBlockNumberY = pos.y / TILES_IN_MINIMAP_BLOCK;

    if (pos.x < 0 || pos.y < 0 || pos.z < 0) {
        return;
    }

    if (!m_isLoadingFinished) {
        return;
    }

    if (currZ != pos.z) {
        m_isFirstRenderIteration = true;
    }
    currZ = pos.z;

    int blockDistanceX = currBlockNumberX - m_prevBlockNumberX;
    int blockDistanceY = currBlockNumberY - m_prevBlockNumberY;
    
    bool redraw = false;
    if (m_isFirstRenderIteration) {
        // everything should be recasched 
        blockDistanceX = blockDistanceY = TEXTURE_CACHE_SIZE;
        redraw = true;
        m_isFirstRenderIteration = false;
    }

    internalUpdate(
        blockDistanceX, blockDistanceY, 
        currBlockNumberX, currBlockNumberY
    );

    if (m_prevBlockNumberX != currBlockNumberX || m_prevBlockNumberY != currBlockNumberY) {
        redraw = true;
    }

    if (redraw) {
        const BlockIdxParams params(currBlockNumberX, currBlockNumberY);
        g_drawQueue->add(new DrawQueueItemFrameBufferStart(m_framebuffer));
        //m_framebuffer->bind();
        for (int i = 0; i < TEXTURE_CACHE_SIZE; ++i) {
            for (int j = 0; j < TEXTURE_CACHE_SIZE; ++j) {
                Rect dest(i * MINIMAP_BLOCK_PIXEL_SIZE, j * MINIMAP_BLOCK_PIXEL_SIZE, Size(MINIMAP_BLOCK_PIXEL_SIZE, MINIMAP_BLOCK_PIXEL_SIZE));

                if (m_TexturesCache[i][j]) {
                    Rect src(0, 0, MINIMAP_BLOCK_PIXEL_SIZE, MINIMAP_BLOCK_PIXEL_SIZE);
                    g_drawQueue->addTexturedRect(dest, m_TexturesCache[i][j], src);
                    //g_painter->drawTexturedRect(dest, m_TexturesCache[i][j]);
                } else {
                    g_drawQueue->addFilledRect(dest, Color::black);
                    //g_painter->setColor(Color::black);
                    //g_painter->drawFilledRect(dest);
                    //g_painter->resetColor();
                }
            }
        }
        //m_framebuffer->release();
        g_drawQueue->add(new DrawQueueItemFrameBufferStop(m_framebuffer));
    }

    m_prevBlockNumberX = currBlockNumberX;
    m_prevBlockNumberY = currBlockNumberY;

    redraw = false;
}

void MinimapCustom::draw(const Rect& screenRect, const Position& mapCenter, float scale, const Color& color)
{
    if(screenRect.isEmpty())
        return;

    g_minimapCustom.update(mapCenter);

    g_drawQueue->add(new DrawQueueItemFrameBufferDraw(m_framebuffer, screenRect, internalCalcFramebufferRect(scale, mapCenter, screenRect)));
    /*
    glDisable(GL_BLEND);
    g_painter->saveAndResetState();
    m_framebuffer->draw(screenRect, internalCalcFramebufferRect(scale, mapCenter));
    g_painter->restoreSavedState();
    glEnable(GL_BLEND);
    */
}

Rect MinimapCustom::internalCalcFramebufferRect(float scale, const Position& cameraCenterPos, const Rect& screenRect)
{
    // calc rect by scale
    Size fbSize = m_framebuffer->getSize();

    Size screenSize = screenRect.size();
    int w = std::ceil((screenSize.width() * MINIMAP_SCREEN_SIZE_FACTOR) / scale);
    int h = std::ceil((screenSize.height() * MINIMAP_SCREEN_SIZE_FACTOR) / scale);

    Rect mapRect(0, 0, w, h);
    mapRect.moveCenter(Point(fbSize.width() / 2, fbSize.height() / 2));

    //move rect by curr pos
    Position pos = normalizePosition(cameraCenterPos);
    int currDiffX = pos.x % TILES_IN_MINIMAP_BLOCK;
    int currDiffY = pos.y % TILES_IN_MINIMAP_BLOCK;
    mapRect.translate(currDiffX * MINIMAP_TILE_SIZE, currDiffY * MINIMAP_TILE_SIZE);
    mapRect.translate(MINIMAP_TILE_SIZE * 1.5f, MINIMAP_TILE_SIZE * 1.5f);
    return mapRect;
}

Position MinimapCustom::getTilePosition(const Point& point, const Rect& screenRect, const Position& mapCenter, float scale)
{
    if (screenRect.isEmpty())
        return Position();

    Point relativeMinimapPoint = point - screenRect.topLeft();
    Rect mapRect = internalCalcFramebufferRect(scale, mapCenter, screenRect);
    
    float xTilesCount = static_cast<float>(mapRect.width()) / static_cast<float>(MINIMAP_TILE_SIZE);
    float yTilesCount = static_cast<float>(mapRect.height())/ static_cast<float>(MINIMAP_TILE_SIZE);

    RectF visibleTilesOffset(0, 0, xTilesCount, yTilesCount);
    visibleTilesOffset.moveCenter(PointF(0,0));

    float realTileWidth = static_cast<float>(screenRect.width()) / xTilesCount;
    float realTileHeight = static_cast<float>(screenRect.height()) / yTilesCount;

    PointF relativeMinimapTilePos(
        static_cast<float>(relativeMinimapPoint.x) / realTileWidth,
        static_cast<float>(relativeMinimapPoint.y) / realTileHeight
    );

    PointF mapOffset = (relativeMinimapTilePos + visibleTilesOffset.topLeft());
    Position retPos(mapOffset.x + mapCenter.x, mapOffset.y + mapCenter.y, mapCenter.z);
    return retPos;
}

Rect MinimapCustom::getTileRect(const Position& pos, Rect screenRect, const Position& mapCenter, float scale)
{
    if (screenRect.isEmpty() || pos.z != mapCenter.z)
        return Rect();

    float tileSize = static_cast<float>(MINIMAP_TILE_SIZE);
    Rect tileRect(0, 0, tileSize, tileSize);

    Rect mapRect = internalCalcFramebufferRect(scale, mapCenter, screenRect);
    
    float xTilesCount = static_cast<float>(mapRect.width()) / static_cast<float>(MINIMAP_TILE_SIZE);
    float yTilesCount = static_cast<float>(mapRect.height()) / static_cast<float>(MINIMAP_TILE_SIZE);

    float realTileWidth = static_cast<float>(screenRect.width()) / xTilesCount;
    float realTileHeight = static_cast<float>(screenRect.height()) / yTilesCount;

    float posDiffX = static_cast<int>(pos.x) - static_cast<int>(mapCenter.x);
    float posDiffY = static_cast<int>(pos.y) - static_cast<int>(mapCenter.y);

    screenRect.translate(posDiffX * realTileWidth, posDiffY * realTileHeight);
    tileRect.moveCenter(screenRect.center());
    return screenRect;
}

void MinimapCustom::internalUpdate(
    int blockDistanceX, int blockDistanceY, 
    int currBlockNumberX, int currBlockNumberY
)
{
    if (blockDistanceX >= TEXTURE_CACHE_SIZE || blockDistanceY >= TEXTURE_CACHE_SIZE) {
        // recasche everything
        internalRecascheEverything(currBlockNumberX, currBlockNumberY);
    } else {
        if (blockDistanceY != 0) {
            internalUpdateRows(blockDistanceX, blockDistanceY);
        }

        if (blockDistanceX != 0) {
            internalUpdateCols(blockDistanceX, currBlockNumberY);
        } 
    }
}

void MinimapCustom::internalRecascheEverything(int currBlockNumberX, int currBlockNumberY)
{
    const BlockIdxParams params(currBlockNumberX, currBlockNumberY);

    m_TexturesCache.clear();
    for (int x = params.blockMinX; x <= params.blockMaxX; ++x) {
        std::deque<TexturePtr> currColumn;
        for (int y = params.blockMinY; y <= params.blockMaxY; ++y) {
            internalAppendTexture(currColumn, x, y);
        }

        m_TexturesCache.emplace_back(std::move(currColumn));
    }
}

void MinimapCustom::internalAppendTexture(std::deque<TexturePtr>& destColumn, int x, int y, bool back)
{
    ImagePtr image = getImage(x, y);
    if(!image){
        if (back) {
            destColumn.emplace_back(nullptr);
        } else {
            destColumn.emplace_front(nullptr);
        }
    } else {
        if (back) {
            destColumn.emplace_back(new Texture(image));
        } else {
            destColumn.emplace_front(new Texture(image));
        }
    }
}

void MinimapCustom::internalUpdateRows(int blockDistanceX, int blockDistanceY)
{
    const BlockIdxParams params(m_prevBlockNumberX, m_prevBlockNumberY);

    int absDistance = std::abs(blockDistanceY);
    for (int i = 1; i <= absDistance; ++i) {
        int y = blockDistanceY < 0 ? params.blockMinY - i : params.blockMaxY + i;
        auto itrX = m_TexturesCache.begin();
        if (blockDistanceX > 0) {
            itrX += blockDistanceX;
        }
        int x = blockDistanceX > 0 ? params.blockMinX + blockDistanceX : params.blockMinX;
        int limit = blockDistanceX < 0 ? params.blockMaxX + blockDistanceX : params.blockMaxX;
        for (x; x <= limit; ++x) {
            
            if (blockDistanceY < 0) {
                internalAppendTexture(*itrX, x, y, false);
                itrX->pop_back();
            }
            else {
                internalAppendTexture(*itrX, x, y);
                itrX->pop_front();
            }
            ++itrX;
        }
    }
}

void MinimapCustom::internalUpdateCols(int blockDistanceX, int currBlockNumberY)
{
    const BlockIdxParams params(m_prevBlockNumberX, currBlockNumberY);

    int absDistance = std::abs(blockDistanceX);
    for (int i = 1; i <= absDistance; ++i) {
        std::deque<TexturePtr> currColumn;

        int x = blockDistanceX < 0 ? params.blockMinX - i : params.blockMaxX + i;
        for (int y = params.blockMinY; y <= params.blockMaxY; ++y) {
            internalAppendTexture(currColumn, x, y);
        }

        if (blockDistanceX < 0) {
            m_TexturesCache.emplace_front(std::move(currColumn));
            m_TexturesCache.pop_back();
        } else {
            m_TexturesCache.emplace_back(std::move(currColumn));
            m_TexturesCache.pop_front();
        }
    }
}

ImagePtr MinimapCustom::getImage(int x, int y)
{
    if (isOutsideTheMap(x, y)) {
        return nullptr;
    }

    OneShotReadBuf osrb(imagesDataCache[y][x][currZ].c_str(), imagesDataCache[y][x][currZ].size());
    std::istream istr(&osrb);

    ImagePtr image;
    apng_data apng;
    if (load_apng(istr, &apng) == 0) {
        image = ImagePtr(new Image(Size(apng.width, apng.height), apng.bpp, apng.pdata));
        free_apng(&apng);
    }
    return image;
}

Position MinimapCustom::normalizePosition(const Position& pos)
{
    Position ret(pos);
    ret.x -= MINIMAP_GENERATIOR_OFFSET;
    ret.y -= MINIMAP_GENERATIOR_OFFSET;

    return ret;
}

bool MinimapCustom::isOutsideTheMap(int blockIdxX, int blockIdxY)
{
    if (blockIdxX < 0 || blockIdxY < 0) {
        return true;
    }

    if (blockIdxX >= IMAGE_EXITS_CACHE_SIZE || blockIdxY >= IMAGE_EXITS_CACHE_SIZE) {
        return true;
    }

    return !m_imageExistanceCache[blockIdxY][blockIdxX][currZ];
}

void MinimapCustom::updateTile(const Position& pos, const TilePtr& tile)
{
    MinimapTile minimapTile;
    if(tile) {
        minimapTile.color = tile->getMinimapColorByte();
        minimapTile.flags |= MinimapTileWasSeen;
        if(!tile->isWalkable(true))
            minimapTile.flags |= MinimapTileNotWalkable;
        if(!tile->isPathable())
            minimapTile.flags |= MinimapTileNotPathable;
        minimapTile.speed = std::min<int>((int)std::ceil(tile->getGroundSpeed() / 10.0f), 255);
    } else {
        minimapTile.color = 255;
        minimapTile.flags |= MinimapTileEmpty;
        minimapTile.speed = 1;
    }

    if(minimapTile != MinimapTile()) {
        MinimapBlock& block = getBlock(pos);
        Point offsetPos = getBlockOffset(Point(pos.x, pos.y));
        block.updateTile(pos.x - offsetPos.x, pos.y - offsetPos.y, minimapTile);
        block.justSaw();
    }
}

const MinimapTile& MinimapCustom::getTile(const Position& pos)
{
    static MinimapTile nulltile;
    if(pos.z <= Otc::MAX_Z && hasBlock(pos)) {
        MinimapBlock& block = getBlock(pos);
        Point offsetPos = getBlockOffset(Point(pos.x, pos.y));
        return block.getTile(pos.x - offsetPos.x, pos.y - offsetPos.y);
    }
    return nulltile;
}

std::pair<MinimapBlock_ptr, MinimapTile> MinimapCustom::threadGetTile(const Position& pos) {
    std::lock_guard<std::mutex> lock(m_lock);
    static MinimapTile nulltile;
    
    if (pos.z <= Otc::MAX_Z && hasBlock(pos)) {
        MinimapBlock_ptr block = m_tileBlocks[pos.z][getBlockIndex(pos)];
        if (block) {
            Point offsetPos = getBlockOffset(Point(pos.x, pos.y));
            return std::make_pair(block, block->getTile(pos.x - offsetPos.x, pos.y - offsetPos.y));
        }
    }
    return std::make_pair(nullptr, nulltile);
}

bool MinimapCustom::loadImage(const std::string& fileName, const Position& topLeft, float colorFactor)
{
    if(colorFactor <= 0.01f)
        colorFactor = 1.0f;

    try {
        ImagePtr image = Image::load(fileName);

        uint8 waterc = Color::to8bit(std::string("#3300cc"));

        // non pathable colors
        Color nonPathableColors[] = {
            std::string("#ffff00"), // yellow
        };

        // non walkable colors
        Color nonWalkableColors[] = {
            std::string("#000000"), // oil, black
            std::string("#006600"), // trees, dark green
            std::string("#ff3300"), // walls, red
            std::string("#666666"), // mountain, grey
            std::string("#ff6600"), // lava, orange
            std::string("#00ff00"), // positon
            std::string("#ccffff"), // ice, very light blue
        };

        for(int y=0;y<image->getHeight();++y) {
            for(int x=0;x<image->getWidth();++x) {
                Color color = *(uint32*)image->getPixel(x,y);
                uint8 c = Color::to8bit(color * colorFactor);
                int flags = 0;

                if(c == waterc || color.a() == 0) {
                    flags |= MinimapTileNotWalkable;
                    c = 255; // alpha
                }

                if(flags != 0) {
                    for(Color &col : nonWalkableColors) {
                        if(col == color) {
                            flags |= MinimapTileNotWalkable;
                            break;
                        }
                    }
                }

                if(flags != 0) {
                    for(Color &col : nonPathableColors) {
                        if(col == color) {
                            flags |= MinimapTileNotPathable;
                            break;
                        }
                    }
                }

                if(c == 255)
                    continue;

                Position pos(topLeft.x + x, topLeft.y + y, topLeft.z);
                MinimapBlock& block = getBlock(pos);
                Point offsetPos = getBlockOffset(Point(pos.x, pos.y));
                MinimapTile& tile = block.getTile(pos.x - offsetPos.x, pos.y - offsetPos.y);
                if(!(tile.flags & MinimapTileWasSeen)) {
                    tile.color = c;
                    tile.flags = flags;
                    block.mustUpdate();
                }
            }
        }
        return true;
    } catch(stdext::exception& e) {
        g_logger.error(stdext::format("failed to load OTMM minimap: %s", e.what()));
        return false;
    }
}

void MinimapCustom::saveImage(const std::string& fileName, const Rect& mapRect)
{
    //TODO
}

bool MinimapCustom::loadOtmm(const std::string& fileName)
{
    try {
        FileStreamPtr fin = g_resources.openFile(fileName, g_game.getFeature(Otc::GameDontCacheFiles));
        if(!fin)
            stdext::throw_exception("unable to open file");

        uint32 signature = fin->getU32();
        if(signature != OTMM_SIGNATURE)
            stdext::throw_exception("invalid OTMM file");

        uint16 start = fin->getU16();
        uint16 version = fin->getU16();
        fin->getU32(); // flags

        switch(version) {
            case 1: {
                fin->getString(); // description
                break;
            }
            default:
                stdext::throw_exception("OTMM version not supported");
        }

        fin->seek(start);

        uint blockSize = MMBLOCK_SIZE * MMBLOCK_SIZE * sizeof(MinimapTile);
        std::vector<uchar> compressBuffer(compressBound(blockSize));
        std::vector<uchar> decompressBuffer(blockSize);

        while(true) {
            Position pos;
            pos.x = fin->getU16();
            pos.y = fin->getU16();
            pos.z = fin->getU8();

            // end of file or file is corrupted
            if(!pos.isValid() || pos.z >= Otc::MAX_Z+1)
                break;

            MinimapBlock& block = getBlock(pos);
            ulong len = fin->getU16();
            ulong destLen = blockSize;
            fin->read(compressBuffer.data(), len);
            int ret = uncompress(decompressBuffer.data(), &destLen, compressBuffer.data(), len);
            if(ret != Z_OK || destLen != blockSize)
                break;

            memcpy((uchar*)&block.getTiles(), decompressBuffer.data(), blockSize);
            block.mustUpdate();
            block.justSaw();
        }

        fin->close();
        return true;
    } catch(stdext::exception& e) {
        g_logger.error(stdext::format("failed to load OTMM minimap: %s", e.what()));
        return false;
    }
}

void MinimapCustom::saveOtmm(const std::string& fileName)
{
    try {
        stdext::timer saveTimer;

        FileStreamPtr fin = g_resources.createFile(fileName);

        //TODO: compression flag with zlib
        uint32 flags = 0;

        // header
        fin->addU32(OTMM_SIGNATURE);
        fin->addU16(0); // data start, will be overwritten later
        fin->addU16(OTMM_VERSION);
        fin->addU32(flags);

        // version 1 header
        fin->addString("OTMM 1.0"); // description

        // go back and rewrite where the map data starts
        uint32 start = fin->tell();
        fin->seek(4);
        fin->addU16(start);
        fin->seek(start);

        uint blockSize = MMBLOCK_SIZE * MMBLOCK_SIZE * sizeof(MinimapTile);
        std::vector<uchar> compressBuffer(compressBound(blockSize));
        const int COMPRESS_LEVEL = 3;

        for(uint8_t z = 0; z <= Otc::MAX_Z; ++z) {
            for(auto& it : m_tileBlocks[z]) {
                int index = it.first;
                MinimapBlock& block = *it.second;
                if(!block.wasSeen())
                    continue;

                Position pos = getIndexPosition(index, z);
                fin->addU16(pos.x);
                fin->addU16(pos.y);
                fin->addU8(pos.z);

                ulong len = blockSize;
                int ret = compress2(compressBuffer.data(), &len, (uchar*)&block.getTiles(), blockSize, COMPRESS_LEVEL);
                VALIDATE(ret == Z_OK);
                fin->addU16(len);
                fin->write(compressBuffer.data(), len);
            }
        }

        // end of file
        Position invalidPos;
        fin->addU16(invalidPos.x);
        fin->addU16(invalidPos.y);
        fin->addU8(invalidPos.z);

        fin->flush();

        fin->close();
    } catch(stdext::exception& e) {
        g_logger.error(stdext::format("failed to save OTMM minimap: %s", e.what()));
    }
}
